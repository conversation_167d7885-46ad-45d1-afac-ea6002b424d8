<?php
class Purchasemod extends CI_Model {

        public function __construct()
        {
                $this->load->database();
				$this->load->library('accessuser');
				$this->load->library('session');
        }



		//start of purchase order
		public function check_purchaseorder_notif($notifid=null){

				$this->db->from('tblpurchase_logs');
				$this->db->where("lnotifid",$notifid);
				$query = $this->db->get();
				$row = $query->row_array();
				if ($query->num_rows() > 0)
				{
					return $row['lid'];
				}else{
					return "";
				}

		}

		public function receiving_ctr()
        {
				$this->db->select("max(lmax_no)");
				$this->db->from("tblnumber_generator");
				$this->db->where('ltransaction_type','Receiving');
				$this->db->order_by('lid',"desc");
				$query = $this->db->get();
				$row = $query->row_array();
				if ($query->num_rows() > 0)
				{
					return $row["max(lmax_no)"];
				}else{
					return 0;
				}
        }

		public function old_purchase_order_ctr()
        {
				$this->db->select("MAX(lid)");
				$this->db->from("tblpurchase_order");
				$this->db->where('lmain_id',$this->session->userdata('main_userid'));
				$query = $this->db->get();
				$row = $query->row_array();
				if ($query->num_rows() > 0)
				{
					return $row["MAX(lid)"];
				}else{
					return 0;
				}
        }

		public function create_purchase_order($purchase_no=null,$gen_refno=null,$notifid=null) {

			// check if user type is doctor 2 = doctor users
				$userincode = $this->session->userdata('eclinic_userid');
				if($this->input->post('txtuserencode')=="" or $this->input->post('txtuserencode')==0){
					$userincode = $this->session->userdata('eclinic_userid');
				}else{
					$userincode = $this->input->post('txtuserencode');
				}
				$supplier_rec=$this->supplier_rec($this->input->post('txtsupplier'));
				$data = array(
					"lpurchaseno"=> $purchase_no,
					"ldate"=> date("Y-m-d",strtotime($this->input->post('txtsalesdate'))),
					"ltime"=> $this->config->item("time"),
					"lcustomerid"=> 0,
					"lmain_id"=> $this->session->userdata('main_userid'),
					"luser"=> $userincode,
					"lrefno"=> $gen_refno,
					"ltopup"=> $this->input->post('txttopup'),
					"lbranch"=> $this->session->userdata('session_branch'),
					"lsupplier"=> $supplier_rec['lid'],
					"lsupplier_name"=> $supplier_rec['lname'],
					"lsupplier_code"=> $supplier_rec['lcode'],
					"lnotifid"=> $notifid,
					"lshipped_name"=> $this->input->post('txtshipped'), 
					"lreference"=> $this->input->post('txtmyrefno'),
					"lterms"=> $this->input->post('txtsalesterms'),
					"laddress"=> $this->input->post('txtsalesaddress'),
					"ldate_recieved"=> date("Y-m-d",strtotime($this->input->post('txtsalesdate'))),
					"lpo_refno"=> $this->input->post('txtpo_refno'),
					"leta_date"=> date("Y-m-d",strtotime($this->input->post('txteta_date'))),
				);
                $this->db->insert('tblpurchase_order', $data);

                $rr_ctr=$this->receiving_ctr()+1;
				$data = array(
        			"ltransaction_type" => 'Receiving',
        			"lmax_no" => $rr_ctr,
				);
                $this->db->insert('tblnumber_generator', $data);
		}

		public function check_if_pono_exist($po)
		{
			$this->db->from("tblpurchase_order");
			$this->db->where('lmain_id',$this->session->userdata('main_userid'));
			$this->db->where('lpurchaseno',$po);
			$query = $this->db->get();
			$row = $query->row_array();
			if ($query->num_rows() > 0)
			{
				return $row;
			}else{
				return array();
			}
		}

		public function cate_purchase_logs($gen_refno,$notifid)
        {

			$userincode = $this->session->userdata('eclinic_userid');
			$data = array(
					"lnotifid"=> $notifid,
					"lsessionid"=> $gen_refno,
					"lmainid"=> $this->session->userdata('main_userid'),
					"luserid"=> $userincode,
				);
                $this->db->insert('tblpurchase_logs', $data);

		}

		public function recordlist_count_purchaseorder()
        {
				//$date_today = $this->config->item('date');
				$this->db->select("count(lid)");
				$this->db->from("tblpurchase_order");
				$this->db->where('lmain_id',$this->session->userdata('main_userid'));
				$query = $this->db->get();
				$row = $query->row_array();
				if ($query->num_rows() > 0)
				{
					return $row["count(lid)"];
				}else{
					return 0;
				}
        }

		public function getrecordlist_purchase_order(){

				$this->db->select("
					tr.*, tr.lid as salesid,
					(select lfname from tblaccount where lid = tr.luser limit 1) as lfname,
					(select llname from tblaccount where lid = tr.luser limit 1) as llname,
					");
				$this->db->from('tblpurchase_order tr');
				$this->db->where('tr.lmain_id',$this->session->userdata('main_userid'));

				if($this->session->userdata('filterdate')!="" && $this->session->userdata('filteryear')!=""){
					$datefilter=$this->session->userdata('filteryear')."-".$this->session->userdata('filterdate');
					$this->db->where("(tr.ldate like '%".$datefilter."%' )");
				}
				$this->db->order_by('tr.lid',"desc");
				$query = $this->db->get();
                return $query->result_array();

		}


		public function getrecord_byrefno($lrefno=null){

			$this->db->select("
					tr.*, tr.lid as salesid,
					(select lfname from tblaccount where lid = tr.luser limit 1) as lfname,
					(select llname from tblaccount where lid = tr.luser limit 1) as llname,
					");
			$this->db->from('tblpurchase_order tr');
			$this->db->where('tr.lmain_id',$this->session->userdata('main_userid'));
			$this->db->where('tr.lrefno',$lrefno);
			$this->db->order_by('tr.lid',"desc");
			$query = $this->db->get();
				$row = $query->row_array();
				if ($query->num_rows() > 0)
				{
					return $row;
				}else{
					return 0;
				}
		}

		public function getrecord_items($refno=null){
			$this->db->select("
					itm.*,
					(select lsession from tblinventory_item where lid = itm.litemid limit 1) as litemsession,
					(select lbrand from tblinventory_item where lid = itm.litemid limit 1) as lbrand,
					(select litemsession from tblinventory where lid = itm.litemid limit 1) as reflitemsession,
					(select lsuppprice from tblinventory where lid =itm.litemid limit 1) as lsuppprice,
					(select lname from tblsupplier where lid =itm.lsupplier limit 1) as my_supplier,
					(select lpurchaseno from tblpurchase_order where lrefno=itm.lrefno limit 1) as my_rrno,
					(select lname from tblitem_location where lid=itm.llocation limit 1) as location_name,
					(select lname from tblbranch where lid=itm.lwarehouse limit 1) as warehouse_name,
					(select lqty from tblpo_itemlist where lid=itm.lpo_itemid limit 1) as max_qty,
					");
			$this->db->from("tblpurchase_item itm");
			$this->db->where('itm.lrefno',$refno);
			
			$query=$this->db->get();
			return $query->result_array();


		}
		public function getrecord_items_print($refno=null){
			$this->db->select("
					itm.*,
					(select lsession from tblinventory_item where lid = itm.litemid limit 1) as litemsession,
					(select litemsession from tblinventory where lid = itm.litemid limit 1) as reflitemsession,
					(select lsuppprice from tblinventory where lid =itm.litemid limit 1) as lsuppprice,
					(select lname from tblsupplier where lid =itm.lsupplier limit 1) as my_supplier,
					(select lpurchaseno from tblpurchase_order where lrefno=itm.lrefno limit 1) as my_rrno,
					(select lpo_number from tblpurchase_order where lrefno=itm.lrefno limit 1) as lpo_number,
					(select lname from tblbranch where lid=itm.lwarehouse limit 1) as lwarehouse,

					");
			$this->db->from("tblpurchase_item itm");
			$this->db->where('itm.lrefno',$refno);
			$this->db->order_by('itm.litem_code',"asc");
			$query=$this->db->get();
			return $query->result_array();


		}


		public function get_records_bynotifid($fields=null,$notifid){

				$this->db->from('tblpurchase_order');
				$this->db->where("lnotifid",$notifid);
				$query = $this->db->get();
				$row = $query->row_array();
				if ($query->num_rows() > 0)
				{
					return $row[$fields];
				}else{
					return "";
				}

		}

		public function recordlist_count_transferinventory()
        {
				//$date_today = $this->config->item('date');
				$this->db->select("
					tr.*,
					(select lfname from tblaccount where lid = tr.lmain_id limit 1) as lfname,
					(select llname from tblaccount where lid = tr.lmain_id limit 1) as llname,
				");
				$this->db->from("tblbranchinventory_transferlist");
				$this->db->where('lmain_id',$this->session->userdata('main_userid'));
				return $this->db->count_all_results();
        }
        
		public function getrecordlist_transferbranch($offset,$currentpage)
        {
        		$this->db->select("
					tr.*,
					(select lfname from tblaccount where lid = tr.lmain_id limit 1) as lfname,
					(select llname from tblaccount where lid = tr.lmain_id limit 1) as llname,
				");
				$this->db->from('tblbranchinventory_transferlist tr');
				$this->db->where('tr.lmain_id',$this->session->userdata('main_userid'));
				$this->db->order_by('tr.lid',"desc");
				$this->db->limit($currentpage, $offset);
				$query = $this->db->get();
                return $query->result_array();

		}
/*		public function insert_purchase_item($refno,$type,$itemid,$name,$desc,$price,$qty=null,$itempoints=null,$variant_main=null,$variant_sub=null,$variant_id=null,$supplier=null,$sup_price=null,$itm_unit=null,$itm_unit_qty=null,$itemrecord=null)
        {

			$data = array(
					"lrefno"=> $refno,
					"ltype"=> $type,
					"litemid"=> $itemid,
					"lname"=> $name,
					"ldesc"=> $desc,
					"lprice"=> $price,
					"lqty"=> $qty,
					"lpoints"=> $itempoints,
					"luser"=> $this->session->userdata('eclinic_userid'),
					"lvariant_main"=> $variant_main,
					"lvariant_sub"=> $variant_sub,
					"lvariant_id"=> $variant_id,
					"loriginal_qty"=> $qty,
					"lsupplier"=> $supplier,
					"lsup_price"=> $sup_price,
					"lunit"=> $itm_unit,
					"lunit_qty"=> $itm_unit_qty,
					"lpartno"=> $itemrecord['lpartno'],
					"litem_code"=> $itemrecord['litemcode'],
				);
                $this->db->insert('tblpurchase_item', $data);
			//$transaction_item_id=$this->get_max_transaction_item_id($refno);
			//return $transaction_item_id;

		//	$inventory = $this->get_item_rec($itemid);

        } */



		public function get_item_rec($itemid) {
			$this->db->where('lid',$itemid);
			$query = $this->db->get('tblinventory_item');
			$row = $query->row_array();
			if ($query->num_rows() > 0)	{
				return $row;
			} else {
					return array();
			}
		}



		public function check_purchase_items($lrefno,$itemid=null){

				$this->db->from('tblpurchase_item');
				$this->db->where("lrefno",$lrefno);
				$this->db->where("litemid",$itemid);
				$query = $this->db->get();
				$row = $query->row_array();
				if ($query->num_rows() > 0)
				{
					return $row['lid'];
				}else{
					return "";
				}

		}

		public function getrecord_purchase_items($lrefno,$itemid=null,$variantid=null){

				$this->db->from('tblpurchase_item');
				$this->db->where("lrefno",$lrefno);
				$this->db->where("litemid",$itemid);
				if($variantid!=""){
					$this->db->where("lvariant_id",$variantid);
				}
				$query = $this->db->get();
				$row = $query->row_array();
				if ($query->num_rows() > 0)
				{
					return $row;
				}else{
					return array();
				}

		}


		public function update_item_qty($purchase_id,$qty=null,$po_itemid=null){
			$data = array(
					'lqty' => $qty
			);
			$this->db->where('lid',$purchase_id);
			$this->db->update("tblpurchase_item", $data);

			$data = array(
					'lreceiving_qty' => $qty
			);
			$this->db->where('lid',$po_itemid);
			$this->db->update("tblpo_itemlist", $data);

			$data = array(
					'lqty' => $qty
			);
			$this->db->where('lid',$purchase_id);
			$this->db->update("tblpurchase_item", $data);

			$data1 = array(
				'lin' => $qty,
				'ltotal' => $qty,
			);
			$this->db->where('lpurchase_item_id',$purchase_id);
			$this->db->update("tblinventory_logs", $data1);

		}

		public function update_item_location($purchase_id,$location=null){
			$data = array(
					'llocation' => $location
			);
			$this->db->where('lid',$purchase_id);
			$this->db->update("tblpurchase_item", $data);
		}

		public function update_item_warehouse($purchase_id,$warehouse=null){
			$data = array(
					'lwarehouse' => $warehouse
			);
			$this->db->where('lid',$purchase_id);
			$this->db->update("tblpurchase_item", $data);
		}

		public function get_my_record_purchase($lrefno){

				$this->db->from('tblpurchase_order');
				$this->db->where("lrefno",$lrefno);
				$query = $this->db->get();
				$row = $query->row_array();
				if ($query->num_rows() > 0)
				{
					return $row;
				}else{
					return array();
				}

		}

		public function get_my_inv_rec($itemid){

				$this->db->from('tblinventory');
				$this->db->where("lid",$itemid);
				$query = $this->db->get();
				$row = $query->row_array();
				if ($query->num_rows() > 0)
				{
					return $row;
				}else{
					return array();
				}

		}

		public function update_purchase_order($lrefno){
			$data = array(
					'ltransaction_status' => $this->input->post('txtstatus'),
			);
			$this->db->where('lrefno',$lrefno);
			$query = $this->db->update("tblpurchase_order", $data);

		}

		public function recordlist_search_count()
        {
				$txtsearch = $this->session->userdata('txtsearch');
				$txtsort = $this->session->userdata('txtsort');
				$txtsortdate= $this->session->userdata('txtsortdate');


				//$txtdatefrom= date("Y-m-d",strtotime($this->input->post('txtdatefrom')));
				//$txtdateto= date("Y-m-d",strtotime($this->input->post('txtdateto')));

				$this->db->select("count(lid)");
				$this->db->from('tblpurchase_order');
				$this->db->where('lmain_id',$this->session->userdata('main_userid'));
				//if($txtdatefrom !="" && $txtdateto!=""){
				//	$this->db->where('(tr.ldate >="'.$txtdatefrom.'" and tr.ldate <="'.$txtdateto.'")');
				//}

				if($txtsearch!=""){
					$this->db->like('lt_lfname',$txtsearch);
					$this->db->or_like('lt_llname',$txtsearch);
					$this->db->or_like('lpurchaseno',$txtsearch);
					//$this->db->like('lfname',$txtsearch);
					//$this->db->or_like('llname',$txtsearch);
					//$this->db->where("(lsaleno like '%".$txtsearch."%')");
				}
				/*
				if($txtsort!=""){
					$this->db->order_by('p.lfname',$txtsort);
				}
				if($txtsortdate!=""){
					$this->db->order_by('tr.lid',$txtsortdate);
				}
				*/
				$query = $this->db->get();
				$row = $query->row_array();
				if ($query->num_rows() > 0)
				{
					return $row["count(lid)"];
				}else{
					return 0;
				}
        }
		public function sort_reclist($offset,$currentpage)
        {
				$txtsearch = $this->session->userdata('txtsearch');
				$txtsort = $this->session->userdata('txtsort');
				$txtsortdate= $this->session->userdata('txtsortdate');

				//$txtdatefrom= date("Y-m-d",strtotime($this->input->post('txtdatefrom')));
				//$txtdateto= date("Y-m-d",strtotime($this->input->post('txtdateto')));

				$this->db->select("
					tr.*, tr.lid as salesid,
					(select lfname from tblpatient where lsessionid = tr.lcustomerid) as lfname,
					(select llname from tblpatient where lsessionid = tr.lcustomerid) as llname,


					");
				$this->db->from('tblpurchase_order tr');
				//$this->db->join('tblpatient p', 'p.lsessionid = tr.lcustomerid',"left");
				$this->db->where('tr.lmain_id',$this->session->userdata('main_userid'));


				//if($txtdatefrom !="" && $txtdateto!=""){
				//	$this->db->where('(tr.ldate >="'.$txtdatefrom.'" and tr.ldate<="'.$txtdateto.'")');
				//}

				if($txtsearch!=""){
					$this->db->like('tr.lt_lfname',$txtsearch);
					$this->db->or_like('tr.lt_llname',$txtsearch);
					$this->db->or_like('tr.lpurchaseno',$txtsearch);
					//$this->db->where("(p.llname like '%".$txtsearch."%')");
					//$this->db->where("(tr.lsaleno like '%".$txtsearch."%')");
				}
				/*
				if($txtsort!=""){
					$this->db->order_by('p.lfname',$txtsort);
				}
				*/
				if($txtsortdate!=""){
					$this->db->order_by('tr.lid',$txtsortdate);
				}

				$this->db->limit($currentpage, $offset);
				$query = $this->db->get();
                return $query->result_array();
        }



		public function clear_purchase($refno)
        {
			$this->db->delete('tblpurchase_order', array('lrefno' => $refno));
			$this->db->delete('tblpurchase_item', array('lrefno' => $refno));
			$this->db->delete('tblinventory_logs', array('lrefno' => $refno));
        }


		//ends of purchase order


		public function update_notif_outstock($itemid){
			$data = array(
					'lstatus' => "Done",
			);
			$this->db->where('litem',$itemid);
			$query = $this->db->update("tbloutstock", $data);

		}
		public function update_notif_counts($itemid){
			$data = array(
					'lout_status' => "1",
			);
			$this->db->where('linv_session',$itemid);
			$query = $this->db->update("tblnotifications", $data);

		}
		public function delete_purchase_item($itemid)
        {
			$this->db->delete('tblpurchase_item', array('lid' => $itemid));
			$this->db->delete('tblinventory_logs', array('lpurchase_item_id' => $itemid));
        }

		public function search_exist_order()
        {

			$date_today = date("Y-m-d");
				$this->db->select("
					tr.*, tr.lid as salesid,
					(select lfname from tblaccount where lid = tr.luser limit 1) as lfname,
					(select llname from tblaccount where lid = tr.luser limit 1) as llname,
					");
				$this->db->from('tblpurchase_order tr');
				$this->db->where('tr.lmain_id',$this->session->userdata('main_userid'));
				$this->db->where('tr.ldate',$date_today);
				$this->db->where('tr.ltransaction_status',"Pending");
				$this->db->order_by('tr.lid',"desc");

				$query = $this->db->get();
                return $query->result_array();

		}
		public function get_my_purchase_order($lid=null)
        {
				//$date_today = $this->config->item('date');
				$this->db->from("tblpurchase_order");
				$this->db->where('lmain_id',$this->session->userdata('main_userid'));
				$this->db->where('lid',$lid);
				$query = $this->db->get();
				$row = $query->row_array();
				if ($query->num_rows() > 0)
				{
					return $row;
				}else{
					return 0;
				}
        }

		public function supplier_list()
        {
				//$date_today = $this->config->item('date');
				$this->db->from("tblsupplier");
				$this->db->where('lmain_id',$this->session->userdata('main_userid'));
				$this->db->where('lstatus',1);
				$this->db->order_by('lname',"asc");
				$query = $this->db->get();
                return $query->result_array();
        }


		public function check_exist_po_no($po_number=null)
        {
				$this->db->from("tblpurchase_order");
				$this->db->where('lmain_id',$this->session->userdata('main_userid'));
				$this->db->where('lpurchaseno',$po_number);
				$query = $this->db->get();
				$row = $query->row_array();
				if ($query->num_rows() > 0)
				{
					return $row['lid'];
				}else{
					return "";
				}
        }
		public function supplier_rec($supplierid=null)
        {
				$this->db->from("tblsupplier");
	//			$this->db->where('lmain_id',$this->session->userdata('main_userid'));
				$this->db->where('lid',$supplierid);
				$query = $this->db->get();
				$row = $query->row_array();
				if ($query->num_rows() > 0)
				{
					return $row;
				}else{
					return array();
				}
        }

		public function check_exist_po_no_purhcase($po_number=null,$lrefno=null)
        {
				$this->db->from("tblpurchase_order");
				$this->db->where('lmain_id',$this->session->userdata('main_userid'));
				$this->db->where('lpurchaseno',$po_number);
				$this->db->where('lrefno !=',$lrefno);
				$query = $this->db->get();
				$row = $query->row_array();
				if ($query->num_rows() > 0)
				{
					return $row['lid'];
				}else{
					return "";
				}
        }


		public function update_purchase_order_record($lrefno=null){


				$supplier_rec=$this->supplier_rec($this->input->post('txtsupplier'));
				/*
				$data = array(
					"lsupplier"=> $supplier_rec['lid'],
					"lsupplier_name"=> $supplier_rec['lname'],
					"lsupplier_code"=> $supplier_rec['lcode'],
					"lshipped_name"=> $this->input->post('txtshipped'),
					"lshipped"=> $this->input->post('txtshipped'),
					"laddress"=> $this->input->post('txtsalesaddress'),
					"ldate_recieved"=> date("Y-m-d",strtotime($this->input->post('txtsalesdate'))),
				);
                $this->db->where('lrefno',$lrefno);
			    $this->db->update("tblpurchase_order", $data);
			    */

			    $data = array(
					"ldate"=> date("Y-m-d",strtotime($this->input->post('txtsalesdate'))),
					"leta_date"=> date("Y-m-d",strtotime($this->input->post('txteta'))),
				);
                $this->db->where('lrefno',$lrefno);
			    $this->db->update("tblpurchase_order", $data);
			    
			    $data1 = array(
					"ldateadded"=> date("Y-m-d",strtotime($this->input->post('txtsalesdate'))),
				);
                $this->db->where('lrefno',$lrefno);
			    $this->db->update("tblinventory_logs", $data1);
		}
		public function update_purchase_order_record_PO($lrefno=null){


			$data1 = array(
				"ldate"=> date("Y-m-d",strtotime($this->input->post('txtsalesdate'))),
			);
			$this->db->where('lrefno',$lrefno);
			$this->db->update("tblpo_list", $data1);
	}

		public function curier_list()
        {
				$this->db->from("tblcurier");
				$this->db->where('lmain_id',$this->session->userdata('main_userid'));
				$this->db->order_by('lname',"desc");
				$query = $this->db->get();
                return $query->result_array();
        }

		public function curier_rec($curierid=null)
        {
				$this->db->from("tblcurier");
				$this->db->where('lmain_id',$this->session->userdata('main_userid'));
				$this->db->where('lid',$curierid);
				$query = $this->db->get();
				$row = $query->row_array();
				if ($query->num_rows() > 0)
				{
					return $row;
				}else{
					return array();
				}
        }

		//denz
		public function get_item_details($item_sessionid=null) {
			$this->db->select("itm.*,
			(select lname from tblunit where lid=itm.lapplication limit 1) as lapplication_name,
			(select lname from tblbrand where lid=itm.lbrand limit 1) as lbrand_name,
			(select lname from tblitem_location where lid=itm.llocation limit 1) as llocation_name,
			");
			$this->db->from('tblinventory_item itm');
			$this->db->where('itm.lsession',$item_sessionid);
			$query = $this->db->get();
			$row = $query->row_array();
				if ($query->num_rows() > 0)
				{
					return $row;
				}else{
					return array();
				}
		}

		public function get_item_details_byid($pid=null) {
			$this->db->select("itm.*,
			(select lname from tblunit where lid=itm.lapplication limit 1) as lapplication_name,
			(select lname from tblbrand where lid=itm.lbrand limit 1) as lbrand_name,
			(select lname from tblitem_location where lid=itm.llocation limit 1) as llocation_name,
			");
			$this->db->from('tblinventory_item itm');
			$this->db->where('itm.lid',$pid);
			$query = $this->db->get();
			$row = $query->row_array();
				if ($query->num_rows() > 0)
				{
					return $row;
				}else{
					return array();
				}
		}


		public function test($test) {
			$data = array(
				"test_varchar" => $test,
			);
			$this->db->insert('tbltest', $data);
		}

		public function get_rr_rec($refno) {
			$this->db->from('tblpurchase_order');
			$this->db->where('lrefno',$refno);
			$query = $this->db->get();
			$row = $query->row_array();
				if ($query->num_rows() > 0)
				{
					return $row;
				}else{
					return array();
				}
		}

		public function check_item_exists($so_refno=null, $item_session=null) {
				$query = $this->db->get_where('tblpurchase_item', array('lrefno' => $so_refno, 'litem_refno' => $item_session));
				
			//	$query->row_array();
				return (!empty($query->result())) ? TRUE : FALSE;
	    }

		public function get_item_exists($so_refno=null, $item_session=null) {
			$query = $this->db->get_where('tblpurchase_item', array('lrefno' => $so_refno, 'litem_refno' => $item_session));

			$row = $query->row_array();
			if ($query->num_rows() > 0) {
				return $row;
			} else {
				return array();
			}
	    }


		public function insert_purchase_item($refno,$qty,$item) {
			$data = array(
				"lrefno" => $refno,
				"litemid" => $item['lid'],
				"ldesc" => $item['ldescription'],
				"lqty" => $qty,
				"luser" => $this->session->userdata('eclinic_userid'),
		//		"lsupplier" => $supplier,
				"lpartno" => $item['lpartno'],
				"litem_code" => $item['litemcode'],
				"litem_refno " => $item['lsession'],
			);
			$this->db->insert('tblpurchase_item', $data);
		}

		public function get_last_purchase_item() {
			$this->db->order_by('lid','desc');
			$query = $this->db->get('tblpurchase_item');
			$row = $query->row_array();
			if ($query->num_rows() > 0)
			{
				return $row['lid'];
			} else {
				return "";
			}
		}

		public function add_item_logs($item,$qty,$refno) {
			$rr=$this->get_rr_rec($refno); //tblpurchase_order
			$purchase_id = $this->get_last_purchase_item();
      		$purchase_note = "Purchase Order";
			$data = array(
				"linvent_id" => $item['lsession'],
				"lin" => $qty,
				"lout" => 0,
				"ltotal" => $qty,
				"ldateadded" => $rr['ldate_recieved'],
				"lprocess_by" => $rr['lpurchaseno'],
				"lnote" => $rr['lsupplier_code'],
				"linventory_id" => $item['lid'],
				"lstatus_logs" => "+",
        		"ltransaction_item_id" => $purchase_note,
				"lpurchase_item_id" => $purchase_id,
				"lrefno" => $refno,
				"llocation" => "Main",
				"lwarehouse" => "Main",
			    "ltransaction_type" => "Receiving",
			);
			$this->db->insert('tblinventory_logs', $data);
		}

		public function check_if_approver($id){
				$this->db->from('tblapprover');
				$this->db->where("lstaff_id",$id);
                $query = $this->db->get();
                $row = $query->row_array();
				if ($query->num_rows() > 0)
				{
					return TRUE;
				}else{
					return FALSE;
				}
		}
		
    public function cancel_purchaseorder_record($refno=null) {

		$data = array('ltransaction_status' => "Cancelled");
		$this->db->where('lrefno',$refno);
		$this->db->update("tblpo_list", $data);

		$data = array('ltransaction_status' => "Cancelled");
		$this->db->where('lpo_refno',$refno);
		$this->db->update("tblpurchase_order", $data);

		$rr=$this->get_rr_rec($refno); //tblpurchase_order
		$purchase_items=$this->get_purchase_items($refno); //for notifications

		foreach ($purchase_items as $row) { //check if item exists in notifications
			$data = array(
				"linvent_id" => $row['litem_refno'],
				"lin" => 0,
				"lout" => $row['lqty'],
				"ltotal" => $row['lqty'],
				"ldateadded" => date('Y-m-d H:i:s'),
				"lprocess_by" => $rr['lpurchaseno'],
				"lnote" => $rr['lsupplier_name'],
				"lsupplier_id" => $rr['lsupplier'],
				"linventory_id" => $row['litemid'],
				"lstatus_logs" => "-",
				"ltransaction_item_id" => "Purchase Order",
				"lpurchase_item_id" => $row['lid'],
				"lrefno" => $refno,
				"llocation" => $row['location_name'],
				"lwarehouse" => $row['warehouse_name'],
			    "ltransaction_type" => "Receiving",
			);
			$this->db->insert('tblinventory_logs', $data);
		}
    }

    public function approve_purchase_order($refno=null) {
		$data = array('ltransaction_status' => "Approved");
		$this->db->where('lrefno',$refno);
		$this->db->update("tblpurchase_order", $data);
    }

     public function update_po_item($pid,$data) {
     		$this->db->where('lid',$pid);
		$this->db->update("tblpo_itemlist", $data);
    }

    public function finalize_purchase_order_unpost($refno=null) {

		$rr=$this->get_rr_rec($refno); //tblpurchase_order

		$data = array(
			'ltransaction_status' => "Pending",
		);
		$this->db->where('lrefno',$refno);
		$this->db->update("tblpurchase_order", $data);

		$this->db->delete('tblinventory_logs', array('lrefno' => $refno));


	}
	public function finalize_purchase_order($refno=null) {

		$rr=$this->get_rr_rec($refno); //tblpurchase_order

		$data = array(
			'ltransaction_status' => "Delivered",
			'ldate_recieved' => date('Y-m-d'),
		);
		$this->db->where('lrefno',$refno);
		$this->db->update("tblpurchase_order", $data);

		
		$purchase_items=$this->get_purchase_items($refno); //for notifications

		foreach ($purchase_items as $row) { //check if item exists in notifications
			$item_exist = $this->check_item_notif($row['litem_refno']);
			if ($item_exist == TRUE) {
				$data2 = array('lstatus' => 0);
				$this->db->update("tblnotifications", $data2);
			}

			$data = array(
				"linvent_id" => $row['litem_refno'],
				"lin" => $row['lqty'],
				"lout" => 0,
				"ltotal" => $row['lqty'],
				"ldateadded" => $rr['ldate']." ".$rr['ltime'],
				"lprocess_by" => $rr['lpurchaseno'],
				"lnote" => $rr['lsupplier_name'],
				"lsupplier_id" => $rr['lsupplier'],
				"linventory_id" => $row['litemid'],
				"lstatus_logs" => "+",
				"ltransaction_item_id" => "Purchase Order",
				"lpurchase_item_id" => $row['lid'],
				"lrefno" => $refno,
				"llocation" => $row['location_name'],
				"lwarehouse" => $row['warehouse_name'],
			    "ltransaction_type" => "Receiving",
			);
			$this->db->insert('tblinventory_logs', $data);
		}
	}

    public function get_purchase_items($refno) { //purchase order refno
    	$query = $this->db->select('itm.*,
    					(select lname from tblitem_location where lid=itm.llocation limit 1) as location_name,
						(select lname from tblbranch where lid=itm.lwarehouse limit 1) as warehouse_name,
					')
    	                  ->where('itm.lrefno', $refno)
    					  ->get('tblpurchase_item itm');
    	return $query->result_array();
    }

    public function check_item_notif($item_refno) { //check if item exists in notifications
    	$query = $this->db->where('lrefno', $item_refno)
    					  ->where('lstatus', 1)
    					  ->where('ltitle', "Inventory")
    					  ->get('tblnotifications');
		$row = $query->row_array();
    	if ($query->num_rows() > 0) {
			return TRUE;
		} else {
			return FALSE;
		}
    }

    public function get_all_purchase($refno)
    {	
    	$this->db->select('ptm.*,
			(select ldate from tblpurchase_order where lrefno=ptm.lrefno) as date,
			(select ltime from tblpurchase_order where lrefno=ptm.lrefno) as time,
			(select lsupplier_code from tblpurchase_order where lrefno=ptm.lrefno) as supplier_code,
			(select lpurchaseno from tblpurchase_order where lrefno=ptm.lrefno) as purchase_no,
    	');
    	$this->db->from('tblpurchase_item ptm');
    	$this->db->where('litem_refno',$refno);
    	$query = $this->db->get();
       	return $query->result_array();
    }

    public function purchase_request_ctr()
    {
			$this->db->select("max(lmax_no)");
			$this->db->from("tblnumber_generator");
			$this->db->where('ltransaction_type','Purchase Request');
			$this->db->order_by('lid',"desc");
			$query = $this->db->get();
			$row = $query->row_array();
			if ($query->num_rows() > 0)
			{
				return $row["max(lmax_no)"];
			}else{
				return 0;
			}
    }
	public function purchase_order_ctr()
    {
			$this->db->select("max(lmax_no)");
			$this->db->from("tblnumber_generator");
			$this->db->where('ltransaction_type','Purchase Order');
			$this->db->order_by('lid',"desc");
			$query = $this->db->get();
			$row = $query->row_array();
			if ($query->num_rows() > 0)
			{
				return $row["max(lmax_no)"];
			}else{
				return 0;
			}
    }

	public function getrecordlist_purchaseorder(){

			$this->db->select("
				tr.*, tr.lid as salesid,
				(select lfname from tblaccount where lid = tr.luser limit 1) as lfname,
				(select llname from tblaccount where lid = tr.luser limit 1) as llname,
				");
			$this->db->from('tblpo_list tr');
			$this->db->where('tr.lmain_id',$this->session->userdata('main_userid'));

			if($this->session->userdata('filterdate')!="" && $this->session->userdata('filteryear')!=""){
				$datefilter=$this->session->userdata('filteryear')."-".$this->session->userdata('filterdate');
				$this->db->where("(tr.ldate like '%".$datefilter."%' )");
			}
			$this->db->order_by('tr.lid',"desc");
			$query = $this->db->get();
            return $query->result_array();

	}

		public function getrecord_purchaseorder($lrefno=null){

			$this->db->select("
					tr.*, tr.lid as salesid, tr.ltransaction_status as ltransaction_status,
					(select lfname from tblaccount where lid = tr.luser limit 1) as lfname,
					(select llname from tblaccount where lid = tr.luser limit 1) as llname,
					");
			$this->db->from('tblpo_list tr');
			$this->db->where('tr.lmain_id',$this->session->userdata('main_userid'));
			$this->db->where('tr.lrefno',$lrefno);
			$this->db->order_by('tr.lid',"desc");
			$query = $this->db->get();
				$row = $query->row_array();
				if ($query->num_rows() > 0)
				{
					return $row;
				}else{
					return 0;
				}
		}

		public function getitems_purchaseorder($refno=null){
			$this->db->select("
					itm.*,
					(select lsession from tblinventory_item where lid = itm.litemid limit 1) as litemsession,
					(select lbrand from tblinventory_item where lid = itm.litemid limit 1) as lbrand,
					(select litemsession from tblinventory where lid = itm.litemid limit 1) as reflitemsession,
					(select lsuppprice from tblinventory where lid =itm.litemid limit 1) as lsuppprice,
					(select lname from tblsupplier where lid =itm.lsupplier limit 1) as my_supplier,
					(select lpurchaseno from tblpurchase_order where lrefno=itm.lrefno limit 1) as my_rrno,
					");
			$this->db->from("tblpo_itemlist itm");
			$this->db->where('itm.lrefno',$refno);
			
			$query=$this->db->get();
			return $query->result_array();


		}

		public function get_dropdown_items_search($search=null,$price=null){
			$this->db->select('*, lsession as id, litemcode as text,
				(SELECT (sum(lin) - sum(lout)) FROM tblinventory_logs WHERE linvent_id = itm.lsession AND lwarehouse = "WH1") as stockonhand,
				(select SUM(lqty) from tblcredit_return_item where linv_refno=itm.lsession or litem_refno=itm.lsession) as return_qty
			');
			$this->db->from('tblinventory_item itm');
			$this->db->where('itm.lnot_inventory',"0");
			$this->db->where('itm.lstatus',1);
			//if ($type!="" && $type!=NULL) {
			//	$this->db->group_by('itm.litemcode');
			//}
			$this->db->where("lpartno like '%".$search."%' ");
			$this->db->or_where("litemcode like '%".$search."%' ");
			$this->db->or_where("lopn_number like '%".$search."%' ");
			$this->db->or_where("lbrand like '%".$search."%' ");
			$this->db->or_where("ldescription like '%".$search."%' ");
			$this->db->or_where("lapplication like '%".$search."%' ");
			$this->db->limit(100);
			$this->db->order_by('itm.litemcode','asc');
			$query = $this->db->get();
            return $query->result_array();
		}

		public function clear_purchaseorder($refno)
        {
			$this->db->delete('tblpo_list', array('lrefno' => $refno));
			$this->db->delete('tblpo_itemlist', array('lrefno' => $refno));
        }

		public function update_poitem_qty($purchase_id,$qty=null){
			$data = array(
					'lqty' => $qty
			);
			$this->db->where('lid',$purchase_id);
			$query = $this->db->update("tblpo_itemlist", $data);
		}

		public function delete_po_item($itemid)
        {
			$this->db->delete('tblpo_itemlist', array('lid' => $itemid));
        }

	//Start Purchase Request
	public function getrecordlist_purchaserequest(){

		$this->db->select("
			tr.*, tr.lid as salesid,
			(select lfname from tblaccount where lid = tr.luser limit 1) as lfname,
			(select llname from tblaccount where lid = tr.luser limit 1) as llname,
			");
		$this->db->from('tblpr_list tr');

		if($this->session->userdata('filterdate')!="" && $this->session->userdata('filteryear')!=""){
			$datefilter=$this->session->userdata('filteryear')."-".$this->session->userdata('filterdate');
			$this->db->where("(tr.ldatetime like '%".$datefilter."%' )");
		}
		$this->db->order_by('tr.lid',"desc");
		$query = $this->db->get();
        return $query->result_array();

	}
	public function create_purchaserequest_record($purchase_request_no=null,$refno=null) {

			$data = array(
				"lprno"=> $purchase_request_no,
				"ldatetime"=> date("Y-m-d h:i:s"),
				"luser"=> $this->session->userdata('eclinic_userid'),
				"lrefno"=> $refno,
				"lremark"=> $this->input->post('txtremark'),
			);
            $this->db->insert('tblpr_list', $data);

            $po_ctr=$this->purchase_request_ctr()+1;
			$data = array(
    			"ltransaction_type" => 'Purchase Request',
    			"lmax_no" => $po_ctr,
			);
            $this->db->insert('tblnumber_generator', $data);
	}
	public function get_supplier_rec($pid){
		$this->db->from("tblsupplier");
		$this->db->where('lid',$pid);
		$query = $this->db->get();
		$supp = $query->row_array();

		return $supp;
	}
	public function get_supplier_cost($supplierid,$itemrefno){
		$this->db->from("tblsupplier_cost");
		$this->db->where('lsupplier_id',$supplierid);
		$this->db->where('litemsession',$itemrefno);
		$query = $this->db->get();
		$cost_rec = $query->row_array();

		return $cost_rec;
	}
	public function insert_purchaserequest_item($refno,$qty,$item,$supplier,$eta) {
		// Check if $item is an array, if not, create a default array with empty values
		if (!is_array($item)) {
			// Create a default item array with empty values for all required keys
			$default_item = array(
				'lsession' => '',
				'lcog' => 0,
				'lpartno' => '',
				'ldescription' => '',
				'litemcode' => '',
				'lopn_number' => '',
				'lbrand' => ''
			);
			// Use the default array instead
			$item = $default_item;
		} else {
			// Ensure all required keys exist
			if (!isset($item['lsession'])) $item['lsession'] = '';
			if (!isset($item['lcog'])) $item['lcog'] = 0;
			if (!isset($item['lpartno'])) $item['lpartno'] = '';
			if (!isset($item['ldescription'])) $item['ldescription'] = '';
			if (!isset($item['litemcode'])) $item['litemcode'] = '';
			if (!isset($item['lopn_number'])) $item['lopn_number'] = '';
			if (!isset($item['lbrand'])) $item['lbrand'] = '';
		}

		$this->db->from("tblsupplier");
		$this->db->where('lid',$supplier);
		$query = $this->db->get();
		$supp = $query->row_array();

		$this->db->from("tblsupplier_cost");
		$this->db->where('lsupplier_id',$supplier);
		$this->db->where('litemsession',$item['lsession']);
		$query = $this->db->get();
		$cost_rec = $query->row_array();

		if(!empty($cost_rec)){
			$cost = $cost_rec['lcost'];
		}else{
			$cost = $item['lcog'];
		}

		$data = array(
			'lpr_no' => $refno,
		);
		$this->db->where('lpartno',$item['lpartno']);
		$this->db->where("(lpr_no IS NULL or lpr_no='' )");
		$this->db->update("tblinquiry_item", $data);

		$data = array(
			"lrefno" => $refno,
			"ldesc" => $item['ldescription'],
			"lqty" => $qty+0,
			"lpart_no" => $item['lpartno'],
			"litem_code" => $item['litemcode'],
			"litem_refno " => $item['lsession'],
			"lopn_number" => $item['lopn_number'],
			"lcost" => $cost+0,
			"lbrand" => $item['lbrand'],
			"lsupp_id" => $supp['lid'],
			"lsupp_code" => $supp['lcode'],
			"lsupp_name" => $supp['lname'],
		);
		$this->db->insert('tblpr_item', $data);
	}

	public function getrecord_purchaserequest($lrefno=null){

		$this->db->select("
				tr.*,
				(select lfname from tblaccount where lid = tr.luser limit 1) as lfname,
				(select llname from tblaccount where lid = tr.luser limit 1) as llname,
				");
		$this->db->from('tblpr_list tr');
		$this->db->where('tr.lrefno',$lrefno);
		$this->db->order_by('tr.lid',"desc");
		$query = $this->db->get();
			$row = $query->row_array();
			if ($query->num_rows() > 0)
			{
				return $row;
			}else{
				return 0;
			}
	}

	public function getitems_purchaserequest($refno=null){

		$this->db->select("
				itm.*,
				(select lname from tblsupplier where lid =itm.lsupp_id limit 1) as my_supplier,
				(select lpurchaseno from tblpo_list where lrefno=itm.lpo_refno limit 1) as my_rrno,
				(select i.lnote from tblreturn_supplier_item i left join tblreturn_supplier t on t.lrefno=i.lrefno where i.linv_refno =itm.litem_refno  and t.lsupp_id=itm.lsupp_id limit 1) as last_rs_note,

				(select i.lrefno from tblreturn_supplier_item i left join tblreturn_supplier t on t.lrefno=i.lrefno where i.linv_refno =itm.litem_refno and t.lsupp_id=itm.lsupp_id limit 1) as last_rs_refno,

				");
		$this->db->from("tblpr_item itm");
		$this->db->where('itm.lrefno',$refno);
		
		$query=$this->db->get();
		//if ($query)
			return $query->result_array();
		//else 
		//	return [];


	}
	public function clear_purchaserequest($refno)
        {
			$this->db->delete('tblpr_list', array('lrefno' => $refno));
			$this->db->delete('tblpr_item', array('lrefno' => $refno));
        }

	public function update_porequestitem_qty($pid,$qty=null){
		$data = array(
				'lqty' => $qty
		);
		$this->db->where('lid',$pid);
		$query = $this->db->update("tblpr_item", $data);
	}

	public function update_pr_item($pid,$data){
		$this->db->where('lid',$pid);
		$query = $this->db->update("tblpr_item", $data);
	}
	public function add_supplier_database($data){
		$this->db->insert('tblsupplier_cost', $data);
	}


	public function update_pr_rec($refno,$data){
		$this->db->where('lrefno',$refno);
		$query = $this->db->update("tblpr_list", $data);
	}


	public function delete_porequest_item($itemid)
        {
			$this->db->delete('tblpr_item', array('lid' => $itemid));
        }

        public function approve_purchase_request($refno=null) {
			$data = array('lapproval' => "Approved");
			$this->db->where('lrefno',$refno);
			$this->db->update("tblpr_list", $data);
	    }

		//End Purchase Request

		

		public function create_purchaseorder_record($data) {
				
				$this->db->insert('tblpo_list', $data);

               			$po_ctr=$this->purchase_order_ctr()+1;
				$data = array(
	        			"ltransaction_type" => 'Purchase Order',
	        			"lmax_no" => $po_ctr,
				);
                		$this->db->insert('tblnumber_generator', $data);
		}


		public function insert_purchaseorder_item($refno,$qty,$item,$supplier,$eta) {
			// Check if $item is an array, if not, create a default array with empty values
			if (!is_array($item) || empty($item)) {
				// Create a default item array with empty values for all required keys
				$item = array(
					'lid' => 0,
					'lsession' => '',
					'lcog' => 0,
					'lpartno' => '',
					'ldescription' => '',
					'litemcode' => '',
					'lopn_number' => '',
					'lbrand' => ''
				);
			} else {
				// Ensure all required keys exist
				if (!isset($item['lid'])) $item['lid'] = 0;
				if (!isset($item['lsession'])) $item['lsession'] = '';
				if (!isset($item['lcog'])) $item['lcog'] = 0;
				if (!isset($item['lpartno'])) $item['lpartno'] = '';
				if (!isset($item['ldescription'])) $item['ldescription'] = '';
				if (!isset($item['litemcode'])) $item['litemcode'] = '';
				if (!isset($item['lopn_number'])) $item['lopn_number'] = '';
				if (!isset($item['lbrand'])) $item['lbrand'] = '';
			}

			$this->db->from("tblsupplier");
			$this->db->where('lid',$supplier);
			$query = $this->db->get();
			$supp = $query->row_array();

			$data = array(
				'lpo_no' => $refno,
			);
			$this->db->where('lpartno',$item['lpartno']);
			$this->db->where("(lpo_no IS NULL or lpo_no='' )");
			$this->db->update("tblinquiry_item", $data);

			$data = array(
				"lrefno" => $refno,
				"litemid" => $item['lid'],
				"ldesc" => $item['ldescription'],
				"lqty" => $qty+0,
				"luser" => $this->session->userdata('eclinic_userid'),
				"lpartno" => $item['lpartno'],
				"litem_code" => $item['litemcode'],
				"litem_refno " => $item['lsession'],
				"lopn_number" => $item['lopn_number'],
				"lsup_price" => $item['lcog'],
				"lbrand" => $item['lbrand'],
				"lsupp_id" => $supp['lid'],
				"lsupp_code" => $supp['lcode'],
				"lsupp_name" => $supp['lname'],
				"leta_date" => $eta,
			);
			$this->db->insert('tblpo_itemlist', $data);
		}

		public function finalize_purchaseorder($refno=null,$itemlist=null) {
			$this->db->select("itm.*,
				(select lname from tblsupplier where lid = itm.lsupp_id limit 1) as supplier_name,
				(select lpurchaseno from tblpo_list where lrefno = itm.lrefno limit 1) as po_number,
				(select llocation from tblinventory_item where lsession = itm.litem_refno limit 1) as location_id,
				(select lid from tblbranch order by lid asc limit 1) as warehouse_id,
			");
			$this->db->from("tblpo_itemlist itm");
			$this->db->where('itm.lrefno',$refno);
			if(!empty($itemlist)){
				$this->db->group_start();
				foreach($itemlist as $pid) {
					$this->db->or_where('itm.lid',$pid);
				}
				$this->db->group_end();

			}
			$this->db->order_by('itm.leta_date','ASC');
			$this->db->order_by('itm.lsupp_code','ASC');
			$query=$this->db->get();
			$purchase_items=$query->result_array();

			$supp_id="";$eta="";$rr_refno="";$rr_no="";
			foreach ($purchase_items as $key) {
				if ($supp_id==$key['lsupp_id'] && $eta==$key['leta_date']) {
			        $data = array(
						"lrefno" => $rr_refno,
						"litemid" => $key['litemid'],
						"ldesc" => $key['ldesc'],
						"lqty" => $key['lqty'],
						"luser" => $this->session->userdata('eclinic_userid'),
						"lpartno" => $key['lpartno'],
						"litem_code" => $key['litem_code'],
						"litem_refno " => $key['litem_refno'],
						"lopn_number" => $key['lopn_number'],
						"lbrand" => $key['lbrand'],
						"lsup_price" => $key['lsup_price']+0,
						"lsupplier" => $key['lsupp_id'],
						"lpo_itemid" => $key['lid'],
						"lpo_qty" => $key['lqty'],
						"llocation" => $key['location_id'],
						"lwarehouse" => $key['warehouse_id'], 
						
					);
					$this->db->insert('tblpurchase_item', $data);

					//mark not listed item
					$data = array(
						'lrr_no' => $rr_refno,
					);
					$this->db->where('lpo_no',$key['lrefno']);
					$this->db->where('lpartno',$key['lpartno']);
					$this->db->update("tblinquiry_item", $data);

					$data = array(
						'lreceiving_refno' => $rr_refno,
						'lreceiving_no' => $rr_no,
						'lreceiving_qty' => $key['lqty']
					);
					$this->db->where('lid',$key['lid']);
					$this->db->update("tblpo_itemlist", $data);
				}

				else{
					$supp_id=$key['lsupp_id'];
					$eta=$key['leta_date'];

					$rr_refno=date("YmdHis").rand(1,9999999);
					$rr_no='RR-'.date('y').str_pad(($this->receiving_ctr()+1), 2, "0", STR_PAD_LEFT);

					$data = array(
						"lpurchaseno"=> $rr_no,
						"ldate"=> date("Y-m-d"),
						"ltime"=> $this->config->item("time"),
						"lcustomerid"=> 0,
						"lmain_id"=> $this->session->userdata('main_userid'),
						"luser"=> $this->session->userdata('eclinic_userid'),
						"lrefno"=> $rr_refno,
						"lbranch"=> $this->session->userdata('session_branch'),
						"lsupplier"=> $key['lsupp_id'],
						"lsupplier_name"=> $key['supplier_name'],
						"lsupplier_code"=> $key['lsupp_code'],
						"lpo_refno"=> $key['lrefno'],
						"leta_date"=> $key['leta_date'],
						"lpo_number"=> $key['po_number'],
					);
	                		$this->db->insert('tblpurchase_order', $data);

	                		$rr_ctr=$this->receiving_ctr()+1;
					$data = array(
		        			"ltransaction_type" => 'Receiving',
		        			"lmax_no" => $rr_ctr,
						);
		                	$this->db->insert('tblnumber_generator', $data);

			        	$data = array(
						"lrefno" => $rr_refno,
						"litemid" => $key['litemid'],
						"ldesc" => $key['ldesc'],
						"lqty" => $key['lqty'],
						"luser" => $this->session->userdata('eclinic_userid'),
						"lpartno" => $key['lpartno'],
						"litem_code" => $key['litem_code'],
						"litem_refno " => $key['litem_refno'],
						"lopn_number" => $key['lopn_number'],
						"lbrand" => $key['lbrand'],
						"lsup_price" => $key['lsup_price']+0,
						"lsupplier" => $key['lsupp_id'],
						"lpo_itemid" => $key['lid'],
						"lpo_qty" => $key['lqty'],
						"llocation" => $key['location_id'],
						"lwarehouse" => $key['warehouse_id'], 
					);
					$this->db->insert('tblpurchase_item', $data);

					//mark not listed item
					$data = array(
						'lrr_no' => $rr_refno,
					);
					$this->db->where('lpo_no',$key['lrefno']);
					$this->db->where('lpartno',$key['lpartno']);
					$this->db->update("tblinquiry_item", $data);

					$data = array(
						'lreceiving_refno' => $rr_refno,
						'lreceiving_no' => $rr_no,
						'lreceiving_qty' => $key['lqty']
					);
					$this->db->where('lid',$key['lid']);
					$this->db->update("tblpo_itemlist", $data);
				}


			}

			$query = $this->db->query('SELECT coalesce(count(lid),0) as ctr FROM tblpo_itemlist WHERE lrefno = "' . $refno . '" and lreceiving_refno is NULL');
			$row = $query->row();
			$po_delivered=$row->ctr;
			if($po_delivered==0){
				$status="Posted";
			}else{
				$status="Partial Delivery";
			}
			$data = array('ltransaction_status' => $status);
			$this->db->where('lrefno',$refno);
			$this->db->update("tblpo_list", $data);
		}

}




